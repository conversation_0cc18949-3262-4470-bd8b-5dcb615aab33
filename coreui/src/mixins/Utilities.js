
export const uuid = () => {
  const head = Math.random()
    .toString(36)
    .substring(2);
  const midd = Date.now().toString(36);
  const tail = Math.random()
    .toString(36)
    .substring(2);
  return `${head}.${midd}.${tail}`;
}


export default {
  selectRange: function* (start, end) {
    yield start;
    if (start === end) return;
    if (start > end) {
      yield* this.selectRange(start - 1, end);
    } else {
      yield* this.selectRange(start + 1, end);
    }
  },
  uuid,
  isEqual(obj1, obj2) {
    return Object.keys(obj1).reduce((acc, item) => {
      acc = acc && obj2[item] === obj1[item];
      return acc;
    }, true);
  },
  /**
   *
   * @param {Date} startDate
   * @param {Date} endDate
   * @returns String
   */
  getHumanReadableTimeElapsed(startDate, endDate) {
    const secondsElapsed = Math.floor((endDate - startDate) / 1000);

    const years = Math.floor(secondsElapsed / 31536000);
    const months = Math.floor((secondsElapsed % 31536000) / 2628000);
    const days = Math.floor(((secondsElapsed % 31536000) % 2628000) / 86400);
    const hours = Math.floor((((secondsElapsed % 31536000) % 2628000) % 86400) / 3600);
    const minutes = Math.floor(((((secondsElapsed % 31536000) % 2628000) % 86400) % 3600) / 60);
    const seconds = secondsElapsed % 60;

    let result = '';

    if (years > 0) {
      result += years + ' year' + (years > 1 ? 's' : '') + ' ';
    }
    if (months > 0) {
      result += months + ' month' + (months > 1 ? 's' : '') + ' ';
    }
    if (days > 0) {
      result += days + ' day' + (days > 1 ? 's' : '') + ' ';
    }
    if (hours > 0) {
      result += hours + ' hour' + (hours > 1 ? 's' : '') + ' ';
    }
    if (minutes > 0) {
      result += minutes + ' minute' + (minutes > 1 ? 's' : '') + ' ';
    }
    if (seconds > 0) {
      result += seconds + ' second' + (seconds > 1 ? 's' : '') + ' ';
    }

    return result.trim();
  },
  /**
   *
   * @param {Date} startDate
   * @param {Date} endDate
   * @returns
   */
  dateDiff(startDate, endDate) {

    const secondsElapsed = Math.floor((endDate - startDate) / 1000);

    const years = Math.floor(secondsElapsed / 31536000);
    const months = Math.floor((secondsElapsed % 31536000) / 2628000);
    const days = Math.floor(((secondsElapsed % 31536000) % 2628000) / 86400);
    const hours = Math.floor((((secondsElapsed % 31536000) % 2628000) % 86400) / 3600);
    const minutes = Math.floor(((((secondsElapsed % 31536000) % 2628000) % 86400) % 3600) / 60);
    const seconds = secondsElapsed % 60;

    return {
      years,
      months,
      days,
      hours,
      minutes,
      seconds,
    };
  }
};
