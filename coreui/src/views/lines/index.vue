
<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-cardHeader> Lines </c-cardHeader>
          <c-card-body>
            <c-button
              :to="{ name: 'CreateLine' }"
              color="primary"
              v-if="checkPermission('create_lines')"
              >Create Line</c-button
            >

            <c-dropdown
              placement="bottom-end"
              toggler-text="Tools"
              color="primary"
              class="float-right d-inline-block"
              ref="toolsBtn"
            >
              <c-dropdown
                placement="left-start"
                toggler-text="Template"
                class="float-right d-inline-block"
                ref="templateDropdown"
              >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('Line_Template.xlsx')"
                  >Line</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('Line_Structure_Template.xlsx')"
                  >Line Structure</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('divisionAndParent.xlsx')"
                  >Divisions</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('parent.xlsx')"
                  >Parent</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('userAndDivision.xlsx')"
                  >Users and Divisions</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('lineProducts.xlsx')"
                  >Line Products</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('lineBricks.xlsx')"
                  >Line Bricks</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('lineSpecialities.xlsx')"
                  >Line Specialities</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('download_all_templates')"
                  @click="templateDownload('lineClasses.xlsx')"
                  >Line Classes</c-dropdown-item
                >
              </c-dropdown>
              <!-- end of template -->
              <c-dropdown
                placement="left-start"
                toggler-text="Upload"
                class="float-right d-inline-block"
                ref="uploadDropdown"
              >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload('/api/importlines', 'Upload New Lines')
                  "
                  >Lines</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/import-line-structure',
                      'Upload Line Structure'
                    )
                  "
                  >Line Structure</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importdivisions',
                      'Upload New Line Divisions'
                    )
                  "
                  >Line Divisions</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importdivisionparents',
                      'Upload New Line Division Parents'
                    )
                  "
                  >Line Division Parents</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importuserdivisions',
                      'Upload Users and Divisions'
                    )
                  "
                  >Users and Divisions</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importlineproducts',
                      'Upload New Line Products'
                    )
                  "
                  >Line Products</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importlinebricks',
                      'Upload New Line Bricks'
                    )
                  "
                  >Line Bricks</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importlinespecialities',
                      'Upload New Line Specialities'
                    )
                  "
                  >Line Specialities</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_lines')"
                  @click="
                    initModalForUpload(
                      '/api/importlineclasses',
                      'Upload New Line Classes'
                    )
                  "
                  >Line Classes</c-dropdown-item
                >
              </c-dropdown>
              <!-- end of upload new -->
              <c-dropdown
                placement="left-start"
                toggler-text="Bulk Edit"
                class="float-right d-inline-block"
                ref="bulkDropdown"
              >
                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelines',
                      'Bulk Edit Lines'
                    )
                  "
                  >Lines</c-dropdown-item
                >

                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelinedivisions',
                      'Bulk Edit Line Divisions'
                    )
                  "
                  >Line Divisions</c-dropdown-item
                >

                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelinedivisionparents',
                      'Bulk Edit Line Division Parents'
                    )
                  "
                  >Line Division Parents</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdateuserDivisions',
                      'Bulk Edit User Divisions'
                    )
                  "
                  >User and Divisions</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelineproducts',
                      'Bulk Edit Line Products'
                    )
                  "
                  >Line Products</c-dropdown-item
                >

                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelinebricks',
                      'Bulk Edit Line Bricks'
                    )
                  "
                  >Line Bricks</c-dropdown-item
                >

                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelinespecialities',
                      'Bulk Edit Line Specialities'
                    )
                  "
                  >Line Specialities</c-dropdown-item
                >

                <c-dropdown-item
                  v-if="checkPermission('import_bulk_edit')"
                  @click="
                    initModalForEditBulk(
                      '/api/importupdatelineclasses',
                      'Bulk Edit Line Classes'
                    )
                  "
                  >Line Classes</c-dropdown-item
                >
              </c-dropdown>
              <!-- end of Bulk Edit -->
              <c-dropdown
                placement="left-start"
                toggler-text="Export"
                class="float-right d-inline-block"
                ref="exportDropdown"
              >
                <c-dropdown-item
                  v-if="checkPermission('export_xlsx_lines')"
                  @click="exportLine()"
                  >Excel</c-dropdown-item
                >
                <!-- <c-dropdown-item v-if="checkPermission('export_xlsx_lines')" ><Export/></c-dropdown-item> -->
                <c-dropdown-item
                  v-if="checkPermission('export_csv_lines')"
                  @click="exportCSV()"
                  >CSV</c-dropdown-item
                >
                <c-dropdown-item
                  v-if="checkPermission('export_pdf_lines')"
                  @click="exportLinePDF()"
                  >PDF</c-dropdown-item
                >
              </c-dropdown>
              <!-- end of export -->

              <c-dropdown
                placement="left-start"
                toggler-text="Send"
                class="float-right d-inline-block"
                ref="sendDropdown"
              >
                <c-dropdown-item
                  v-if="checkPermission('export_email_lines')"
                  @click="sendModal = true"
                  >Send to Mail</c-dropdown-item
                >
              </c-dropdown>
              <!-- end of send mail -->
            </c-dropdown>

            <c-modal :title="title" color="success" :show.sync="successModal">
              <CInputFile
                type="file"
                ref="file"
                id="file"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <CProgress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button
                  class="text-white"
                  @click="successModal = false"
                  color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importFiles"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <!-- bulk edit - update modal -->
            <c-modal :title="title" color="success" :show.sync="updateModal">
              <c-input-file
                type="file"
                ref="file"
                id="bulkEditFile"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="updateModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importFiles"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <c-modal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input
                    v-model="email"
                    :tags="emails"
                    name="email[]"
                    :validation="validation"
                    placeholder="To"
                    :add-on-key="addOnKey"
                    @tags-changed="(newEmails) => (emails = newEmails)"
                  />
                </div>
              </template>
              <CTextarea
                label="Message:"
                type="text"
                name="text"
                v-model="text"
                placeholder="Message"
              ></CTextarea>
              <template #footer>
                <c-button
                  @click="sendModal = false"
                  class="text-white"
                  color="danger"
                  >Discard</c-button
                >
                <c-button
                  class="text-white"
                  @click="sendLineMail"
                  color="success"
                  >Send</c-button
                >
              </template>
            </c-modal>
            <c-data-table
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="100"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>

              <template #notes="{ item }">
                <td v-if="item.notes">{{ item.notes.substring(0, 15) }}...</td>
                <td v-else-if="!item.notes"></td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      :to="{ name: 'LineView', params: { id: item.id } }"
                      v-if="checkPermission('show_single_lines')"
                      ><CIcon name="cil-magnifying-glass"
                    /></c-button>
                    <c-button
                      color="success"
                      class="btn-sm mt-2 mr-1"
                      :to="{ name: 'EditLine', params: { id: item.id } }"
                      v-if="checkPermission('edit_lines')"
                      ><i class="cil-pencil"></i><CIcon name="cil-pencil"
                    /></c-button>
                    <c-button
                      color="danger"
                      v-if="checkPermission('delete_lines')"
                      class="btn-sm mt-2 mr-1"
                      @click="getDeleteMessage(item)"
                      ><c-icon name="cil-trash"
                    /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
          </c-card-body>
          <c-card-footer>
            <c-button
              style="float: right"
              class="text-white"
              color="primary"
              @click="replicate()"
              >Replicate</c-button
            >
          </c-card-footer>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
import VueTagsInput from "@johmun/vue-tags-input";
import replicateLine from "../../components/common/ReplicateLine.vue";
export default {
  name: "Lines",
  components: {
    VueTagsInput,
    replicateLine,
  },

  data() {
    return {
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: [
        "id",
        "name",
        "sort",
        "currency name",
        "country name",
        "from_date",
        "to_date",
        "actions",
      ],
      options: [
        { id: 1, name: "Division Types" },
        { id: 2, name: "Divisions" },
        { id: 3, name: "Parents" },
        { id: 4, name: "Bricks" },
      ],
      apiRoute: null,
      title: null,
      successModal: false,
      updateModal: false,
      sendModal: false,
      message: "",
      file: "",
      file_name: "",
      myResult: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
    };
  },
  methods: {
    setclickedItem(clickedItem) {
      this.clickedItem = clickedItem;
      this.showSub = !this.showSub;
    },
    initModalForUpload(api, title) {
      this.successModal = true;
      this.apiRoute = api;
      this.title = title;
    },
    initModalForEditBulk(api, title) {
      this.updateModal = true;
      this.apiRoute = api;
      this.title = title;
    },
    func(message, id) {
      try {
         this.$dialog.open("Delete", message, {
          color: "danger"
        })
        .then((confirmed) => {
          if (confirmed) {
            this.deleteLine(id);
          }
        });
      } catch (error) {
        console.log(error);
        
      }
     
    },
    importFiles() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile(this.apiRoute, formData);
      this.reset();
    },
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    reset() {
      this.file = "";
      this.file_name = "";
      document.getElementById("file").value = "";
      document.getElementById("bulkEditFile").value = "";
      this.successModal = false;
      this.updateModal = false;
    },
    exportLine() {
      this.exportFile("/api/exportlines", "lines.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/exportlinescsv", "Lines.csv");
    },
    exportLinePDF() {
      this.exportFile("/api/exportlinepdf", "Lines.pdf");
    },
    sendLineMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/sendmaillines", formData);
      this.successModal = false;
    },
    removeLine(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    getDeleteMessage(item) {
      axios
        .get(`/api/lines/${item.id}`)
        .then((response) => {
          let message = response.data.message;
          this.func(message, item.id);
        })
        .catch((err) => this.showErrorMessage(err));
    },
    deleteLine(id) {
      axios
        .delete(`/api/lines/${id}`)
        .then((res) => {
          this.removeLine(id);
          this.flash("Line Deleted Successfully");
        })
        .catch(() => this.flash("ooops something Error"));
    },
    initialize() {
      this.$refs.templateDropdown.$el.children[0].style.width = "6rem";
      this.$refs.uploadDropdown.$el.children[0].style.width = "6rem";
      this.$refs.bulkDropdown.$el.children[0].style.width = "6rem";
      this.$refs.exportDropdown.$el.children[0].style.width = "6rem";
      this.$refs.sendDropdown.$el.children[0].style.width = "6rem";

      this.$refs.templateDropdown.$el.children[0].classList.remove(
        "dropdown-toggle"
      );
      this.$refs.uploadDropdown.$el.children[0].classList.remove(
        "dropdown-toggle"
      );
      this.$refs.bulkDropdown.$el.children[0].classList.remove(
        "dropdown-toggle"
      );
      this.$refs.exportDropdown.$el.children[0].classList.remove(
        "dropdown-toggle"
      );
      this.$refs.sendDropdown.$el.children[0].classList.remove(
        "dropdown-toggle"
      );

      this.$refs.toolsBtn.$el.children[1].style.setProperty(
        "min-width",
        "6rem",
        "important"
      );
      this.$refs.toolsBtn.$el.children[1].style.setProperty(
        "padding",
        "0",
        "important"
      );
    },
    replicate() {
      this.$root.$line("Copy From Details", this.items, this.options);
      this.getData();
    },
    getData() {
      this.get("/api/lines", "lines");
    },
  },
  mounted() {
    this.initialize();
  },
  created() {
    this.getData();
  },
};
</script>
