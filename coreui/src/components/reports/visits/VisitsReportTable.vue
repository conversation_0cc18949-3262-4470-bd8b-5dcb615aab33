<template>
  <div class="visits-report-table-container">
    <!-- Vue2DataTable with all visits report functionality -->
    <Vue2DataTable
      :data-source="items"
      :columns="processedColumns"
      :search-term="searchTerm"
      :selected-items="selectedItems"
      :select-all="selectAll"
      :selectable="hasSelectionColumn"
      :loading="isLoading"
      :initial-page-size="25"
      :page-size-options="[10, 25, 50, 100, 200]"
      :virtual-scroll-enabled="true"
      :virtual-scroll-threshold="1000"
      :row-height="50"
      :show-search="true"
      :show-pagination="true"
      :show-total-bar="true"
      search-placeholder="Search visits in all fields..."
      @search="handleSearch"
      @sort="handleSort"
      @selection-change="handleSelectionChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <!-- Comment field with show more/less functionality -->
      <template #comment="{ item, value }">
        <div class="comment-cell">
          <div v-if="!readMore[item.id]" @click="handleShowMore(item.id)" class="comment-preview">
            {{ getCommentPreview(value) }}
            <span v-if="hasLongComment(value)" class="show-more-btn">show more</span>
          </div>
          <div v-else @click="handleShowLess(item.id)" class="comment-full">
            {{ value }}
            <span class="show-less-btn">show less</span>
          </div>
        </div>
      </template>

      <!-- Division field -->
      <template #division="{ value }">
        <strong class="division-text">{{ value }}</strong>
      </template>

      <!-- Date fields -->
      <template #start="{ value }">
        <span class="date-cell">{{ formatDate(value) }}</span>
      </template>

      <template #end="{ value }">
        <span class="date-cell">{{ formatDate(value) }}</span>
      </template>

      <!-- Account field -->
      <template #account="{ item, value }">
        <strong class="account-link" @click="handleAccountClick(item)">{{ value }}</strong>
      </template>

      <!-- Doctor field -->
      <template #doctor="{ item, value }">
        <strong class="doctor-link" @click="handleDoctorClick(item)">{{ value }}</strong>
      </template>

      <!-- Attachments field -->
      <template #attachments="{ value }">
        <div class="attachments-cell">
          <template v-if="value">
            <span v-for="(attachment, index) in getAttachmentList(value)" :key="index" class="attachment-item">
              <a :href="attachment.url" target="_blank" class="attachment-link">{{ attachment.name }}</a>
              <span v-if="index < getAttachmentList(value).length - 1">, </span>
            </span>
          </template>
          <span v-else class="no-attachments">-</span>
        </div>
      </template>

      <!-- Type field -->
      <template #type="{ item, value }">
        <strong :class="getTypeClass(item)">{{ value }}</strong>
      </template>

      <!-- Account type field -->
      <template #acc_type="{ item, value }">
        <strong :class="getAccTypeClass(item)">{{ value }}</strong>
      </template>

      <!-- Visit shift field -->
      <template #visit_shift_id="{ item }">
        <strong :class="getVisitShiftClass(item)">{{ item.acc_type }}</strong>
      </template>

      <!-- Status field -->
      <template #status="{ value }">
        <strong :class="getStatusClass(value)">{{ value }}</strong>
      </template>

      <!-- Details button -->
      <template #v_details="{ item, index }">
        <CButton
          color="primary"
          variant="outline"
          size="sm"
          class="details-btn"
          @click="handleToggleDetails(item, index)"
        >
          {{ item._toggled ? "Hide" : "Details" }}
        </CButton>
      </template>

      <!-- Map button -->
      <template #map="{ item }">
        <CButton
          v-if="checkPermission('show_single_actual_visits_locations')"
          color="primary"
          size="sm"
          class="map-btn"
          @click="handleGetLocation(item)"
        >
          <CIcon name="cil-location-pin" />
        </CButton>
      </template>

      <!-- Plan actions -->
      <template #plan_actions="{ item }">
        <div class="action-buttons">
          <CButton
            v-if="checkPermission('edit_plan_visits')"
            color="success"
            size="sm"
            class="action-btn"
            :to="{ name: 'EditPlanVisit', params: { id: item.id } }"
          >
            <CIcon name="cil-pencil" />
          </CButton>
          <CButton
            v-if="checkPermission('delete_plan_visits')"
            color="danger"
            size="sm"
            class="action-btn"
            @click="handleDeletePlan(item)"
          >
            <CIcon name="cil-trash" />
          </CButton>
        </div>
      </template>

      <!-- Actual actions -->
      <template #actual_actions="{ item }">
        <div class="action-buttons">
          <CButton
            v-if="checkPermission('show_single_actual_visits')"
            color="primary"
            size="sm"
            class="action-btn"
            :to="{ name: 'ActualVisit', params: { id: item.id } }"
            target="_blank"
          >
            <CIcon name="cil-magnifying-glass" />
          </CButton>
          <CButton
            v-if="checkPermission('create_pv_request')"
            color="warning"
            size="sm"
            class="action-btn text-white"
            :to="{ name: 'PV', params: { id: item.id } }"
            target="_blank"
          >
            PV
          </CButton>
          <CButton
            v-if="checkPermission('edit_actual_visits')"
            color="success"
            size="sm"
            class="action-btn"
            :to="{ name: 'EditActualVisit', params: { id: item.id } }"
            target="_blank"
          >
            <CIcon name="cil-pencil" />
          </CButton>
          <CButton
            v-if="checkPermission('delete_actual_visits')"
            color="danger"
            size="sm"
            class="action-btn"
            @click="handleDeleteActualVisit(item)"
          >
            <CIcon name="cil-trash" />
          </CButton>
        </div>
      </template>
      <!-- OW actions -->
      <template #ow_actions="{ item }">
        <div class="action-buttons">
          <CButton
            v-if="checkPermission('edit_ow_actual_visits')"
            color="success"
            size="sm"
            class="action-btn"
            :to="{ name: 'EditOwActualVisit', params: { id: item.id } }"
          >
            <CIcon name="cil-pencil" />
          </CButton>
          <CButton
            v-if="checkPermission('delete_ow_actual_visits')"
            color="danger"
            size="sm"
            class="action-btn"
            @click="handleDeleteOwActual(item)"
          >
            <CIcon name="cil-trash" />
          </CButton>
        </div>
      </template>
    </Vue2DataTable>
  </div>
</template>

<script>
import moment from "moment";
import Vue2DataTable from "../../common/Vue2DataTable";

export default {
  name: 'VisitsReportTable',
  components: {
    Vue2DataTable
  },

  props: {
    items: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    readMore: {
      type: Object,
      required: true
    },
    searchTerm: {
      type: String,
      default: ''
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedItems: []
    };
  },

  computed: {
    // Convert fields array to columns configuration for Vue2DataTable
    processedColumns() {
      return this.fields
        .filter(field => field !== 's') // Remove selection field as it's handled by selectable prop
        .map(field => ({
          key: field,
          label: this.formatFieldName(field),
          sortable: !this.isActionColumn(field),
          searchable: !this.isActionColumn(field),
          type: this.getColumnType(field)
        }));
    },

    // Check if selection column should be shown
    hasSelectionColumn() {
      return this.fields.includes('s');
    }
  },

  methods: {
    // This method is kept for compatibility but Vue2DataTable handles item keys internally
    getItemKey(item, index) {
      return item.id || `item-${index}`;
    },

    // Format field name for display
    formatFieldName(field) {
      if (!field) return '';
      const withSpaces = field.replace(/[_-]/g, ' ');
      return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
    },

    // Get column type
    getColumnType(field) {
      if (['start', 'end', 'created_at', 'updated_at', 'visit_date'].includes(field)) {
        return 'date';
      }
      return 'text';
    },

    // Check if column is an action column
    isActionColumn(field) {
      return ['plan_actions', 'actual_actions', 'ow_actions', 'map', 'v_details'].includes(field);
    },

    // Format date
    formatDate(value) {
      if (!value) return '';
      return moment(String(value)).format("YYYY-MM-DD HH:mm:ss");
    },

    // Comment handling methods
    getCommentPreview(value) {
      if (!value || value.length <= 30) return value;
      return value.substring(0, 30) + '..';
    },

    hasLongComment(value) {
      return value && value.length > 30;
    },

    // Attachment handling
    getAttachmentList(value) {
      if (!value) return [];
      return value.split(', ').map(path => ({
        url: path,
        name: path.split('/').pop()
      }));
    },

    // Style class methods
    getTypeClass(item) {
      return {
        'type-automatic': item.is_automatic == 1,
        'type-manual': item.is_automatic != 1
      };
    },

    getAccTypeClass(item) {
      return {
        'acc-shift-1': item.acc_shift_id == 1,
        'acc-shift-2': item.acc_shift_id == 2,
        'acc-shift-3': item.acc_shift_id == 3
      };
    },

    getVisitShiftClass(item) {
      return {
        'visit-shift-1': item.visit_shift_id == 1,
        'visit-shift-2': item.visit_shift_id == 2,
        'visit-shift-3': item.visit_shift_id == 3
      };
    },

    getStatusClass(value) {
      return {
        'status-approved': value === 'Approved',
        'status-disapproved': value === 'Disapproved',
        'status-pending': value === 'Pending'
      };
    },

    // Event handlers
    handleSearch(payload) {
      // Vue2DataTable sends a payload object with searchTerm and results
      this.$emit('search', payload.searchTerm || payload);
    },

    handleSort(payload) {
      // Vue2DataTable sends a payload object with column and direction
      this.$emit('sort', payload);
    },

    handleSelectionChange(selectedItems) {
      this.selectedItems = selectedItems;
      this.$emit('item-selection-changed', selectedItems);
      this.$emit('update:selected', selectedItems);
    },

    handlePageChange(page) {
      // Vue2DataTable handles pagination internally
      this.$emit('page-change', page);
    },

    handlePageSizeChange(pageSize) {
      // Vue2DataTable handles page size changes internally
      this.$emit('page-size-change', pageSize);
    },

    handleShowMore(itemId) {
      this.$emit('showMore', itemId);
    },

    handleShowLess(itemId) {
      this.$emit('showLess', itemId);
    },

    handleToggleDetails(item, index) {
      this.$emit('toggleDetails', item, index);
    },

    handleAccountClick(item) {
      if (this.$root.$account) {
        this.$root.$account('Account Data', item.account_id);
      }
    },

    handleDoctorClick(item) {
      if (this.$root.$doctor) {
        this.$root.$doctor('Doctor Data', item.doctor_id);
      }
    },

    handleGetLocation(item) {
      this.$emit('getLocation', item);
    },

    handleDeletePlan(item) {
      this.$dialog.open('Delete', 'Do you want to delete this record?', {
        color: 'red',
        width: 290,
        zIndex: 200,
      }).then((confirmed) => {
        if (confirmed) {
          this.$emit('deletePlan', item);
        }
      });
    },

    handleDeleteActualVisit(item) {
      this.$dialog.open('Delete', 'Do you want to delete this record?', {
        color: 'red',
        width: 290,
        zIndex: 200,
      }).then((confirmed) => {
        if (confirmed) {
          this.$emit('deleteActualVisit', item);
        }
      });
    },

    handleDeleteOwActual(item) {
      this.$dialog.open('Delete', 'Do you want to delete this record?', {
        color: 'red',
        width: 290,
        zIndex: 200,
      }).then((confirmed) => {
        if (confirmed) {
          this.$emit('deleteOwActual', item);
        }
      });
    },

    // Permission check
    checkPermission(permission) {
      return this.$parent.checkPermission ? this.$parent.checkPermission(permission) : true;
    }
  },

  // Events emitted by this component
  emits: [
    'showMore',
    'showLess',
    'toggleDetails',
    'getLocation',
    'deletePlan',
    'deleteActualVisit',
    'deleteOwActual',
    'update:selected',
    'update:selectAll',
    'sort',
    'search',
    'item-selection-changed',
    'page-change',
    'page-size-change'
  ]
};
</script>

<style scoped>
/* Custom styles for visits report table */
.visits-report-table-container {
  width: 100%;
}

/* Comment cell styles */
.comment-cell {
  max-width: 200px;
}

.comment-preview, .comment-full {
  cursor: pointer;
}

.show-more-btn, .show-less-btn {
  color: #3b82f6;
  font-size: 12px;
  font-weight: 500;
  display: block;
  margin-top: 4px;
}

.show-more-btn:hover, .show-less-btn:hover {
  color: #1d4ed8;
}

/* Field-specific styles */
.division-text {
  color: #059669;
  font-weight: 600;
}

.date-cell {
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
}

.account-link, .doctor-link {
  color: #3b82f6;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

.account-link:hover, .doctor-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Attachments styles */
.attachments-cell {
  max-width: 150px;
}

.attachment-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 12px;
}

.attachment-link:hover {
  text-decoration: underline;
}

.no-attachments {
  color: #9ca3af;
  font-style: italic;
}

/* Type and status badge styles */
.type-automatic {
  color: #f1c40f;
}

.type-manual {
  color: #374151;
}

.acc-shift-1 {
  color: #efa609;
}

.acc-shift-2 {
  color: #ef09c2;
}

.acc-shift-3 {
  color: #09efde;
}

.visit-shift-1 {
  color: #efa609;
}

.visit-shift-2 {
  color: #ef09c2;
}

.visit-shift-3 {
  color: #09efde;
}

.status-approved {
  color: #059669;
}

.status-disapproved {
  color: #dc2626;
}

.status-pending {
  color: #2563eb;
}

/* Action button styles */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-btn {
  min-width: auto;
}

.details-btn, .map-btn {
  min-width: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .action-buttons {
    justify-content: center;
  }

  .comment-cell {
    max-width: 150px;
  }

  .attachments-cell {
    max-width: 100px;
  }
}
</style>
