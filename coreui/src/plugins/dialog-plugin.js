import GlobalDialog from '../components/common/MyDialog.vue';

const DialogPlugin = {
  install(Vue, options = {}) {
    // Create a constructor for the dialog component
    const DialogConstructor = Vue.extend(GlobalDialog);

    // Create a global instance
    let dialogInstance = null;

    // Function to create dialog instance with proper store injection
    function createDialogInstance(context) {
      // Get store from the calling component's context
      const store = context.$store || (context.$root && context.$root.$store);
      const parent = context.$root || context;

      console.log('Creating dialog with store:', !!store); // Debug log

      return new DialogConstructor({
        store, // Inject the store into the dialog instance
        parent // Set parent for proper inheritance
      });
    }

    // Create the $dialog method
    Vue.prototype.$dialog = {
      open(title, message, options = {}) {
        // Create instance if it doesn't exist
        if (!dialogInstance) {
          dialogInstance = createDialogInstance(this);
          dialogInstance.$mount();
          document.body.appendChild(dialogInstance.$el);
        }

        // Call the open method
        return dialogInstance.open(title, message, options);
      }
    };

    // Also make it available as a global property
    Vue.prototype.$globalDialog = Vue.prototype.$dialog;
  }
};

export default DialogPlugin;