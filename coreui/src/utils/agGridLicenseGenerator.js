import { MD5 } from "../services/md5";


export const renewAgGridLicense = (date = '8/28/2030 5:28:37 AM') => {
    let currentDate = new Date(date);
    let base64Date = btoa(currentDate.getTime().toString());
    const _md5 = new MD5()
    const salt = _md5.md5("hello")
    const licenseBefore = `${salt}[v28.2.1]_${base64Date}`
    const hash = _md5.md5(licenseBefore)
    return `${licenseBefore}${hash}`;
}

export const renewLicense = (date = '8/28/2030 5:28:37 AM') => {
    let reversedDecodeStr = ''; // To store the reversed decoded string
    let k = 0; // Initialize k
    const pkey = [5439488, 7929856, 5111808, 6488064, 4587520, 7667712, 5439488, 6881280,
        5177344, 7208960, 4194304, 4456448, 6619136, 7733248, 5242880, 7077888,
        6356992, 7602176, 4587520, 7274496, 7471104, 7143424];
    const buffr = `essentialstudio;22.2.5;${date};202308210528372383`;

    for (let i = 0; i < buffr.length; i++, k++) {
        if (k === pkey.length) {
            k = 0;
        }
        let c = buffr.charCodeAt(i);
        // Reverse the XOR operation by applying XOR with the pkey value
        reversedDecodeStr += String.fromCharCode(c ^ (pkey[parseInt(k.toString(), 10)] >> 16));
    }
    return btoa(reversedDecodeStr)
}

export const decryptKey = (data) => {
    let reversedDecodeStr = ''; // To store the reversed decoded string
    let k = 0; // Initialize k
    const pkey = [5439488, 7929856, 5111808, 6488064, 4587520, 7667712, 5439488, 6881280,
        5177344, 7208960, 4194304, 4456448, 6619136, 7733248, 5242880, 7077888,
        6356992, 7602176, 4587520, 7274496, 7471104, 7143424];
    const buffr = `essentialstudio;22.2.5;${date};202308210528372383`;

    for (let i = 0; i < buffr.length; i++, k++) {
        if (k === pkey.length) {
            k = 0;
        }
        let c = buffr.charCodeAt(i);
        // Reverse the XOR operation by applying XOR with the pkey value
        reversedDecodeStr += String.fromCharCode(c ^ (pkey[parseInt(k.toString(), 10)] >> 16));
    }
    return btoa(reversedDecodeStr)
}