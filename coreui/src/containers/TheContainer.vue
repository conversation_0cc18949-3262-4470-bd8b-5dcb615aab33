<template>
  <div
    :class="{
      'c-dark-theme': $store.state.darkMode,
      'v-application': vApp,
      'c-app': !vApp,
    }"
    data-app
  >
    <TheSidebar :locale="locale" />
<!--    <TheAside />-->
    <CWrapper>
      <TheHeader v-on:change-locale="changeLocale" />
      <div class="c-body">
        <div class="c-main">
          <CContainer fluid>
            <message></message>
            <!-- <my-dialog ref="confirm"></my-dialog> -->
            <print-dialog ref="print"></print-dialog>
            <approval-dialog ref="approval"></approval-dialog>
            <samples ref="samples"></samples>
            <map-dialog ref="map"></map-dialog>
            <account-dialog ref="account"></account-dialog>
            <doctor-dialog ref="doctor"></doctor-dialog>
            <table-dialog ref="table"></table-dialog>
            <flow-dialog ref="flow"></flow-dialog>
            <request-flow-dialog ref="requestFlow"></request-flow-dialog>
            <clarification-dialog ref="clarification"></clarification-dialog>
            <table-expense-dialog ref="tableExpense"></table-expense-dialog>
            <table-expense-with-approval-dialog
              ref="tableWithApprovalExpense"
            ></table-expense-with-approval-dialog>
            <replicate-line ref="line"></replicate-line>
            <replicate-mapping ref="mapping"></replicate-mapping>
            <plan-dialog ref="plan"></plan-dialog>
            <actual-dialog ref="actual"></actual-dialog>
            <vacation-dialog ref="vacation"></vacation-dialog>
            <commercial-dialog ref="commercial"></commercial-dialog>
            <expense-dialog ref="expense"></expense-dialog>
            <!-- <ramadan-dialog ref="ramadan"></ramadan-dialog> -->
            <call-dialog ref="call"></call-dialog>
            <socket-handler></socket-handler>
            <progress-bar ref="progress"></progress-bar>
            <c-toaster :closeButton="false" v-if="imPersonated">
              <template>
                <c-toast
                  :show="imPersonated"
                  :header="`You are using account of ${authUser.name}`"
                >
                  <c-button color="primary" @click="rollback"
                    >Rollback</c-button
                  >
                </c-toast>
              </template>
            </c-toaster>
            <transition name="fade">
              <router-view />
            </transition>
          </CContainer>
        </div>
        <TheFooter />
      </div>
    </CWrapper>
  </div>
</template>

<script>
// import MyDialog from "../components/common/MyDialog.vue";
import Samples from "../components/common/editSamples.vue";
import PrintDialog from "../components/common/PrintDialog.vue";
import MapDialog from "../components/common/MapDialog.vue";
import DoctorDialog from "../components/common/DoctorDialog.vue";
import AccountDialog from "../components/common/AccountDialog.vue";
import TableDialog from "../components/common/TableDialog.vue";
import ClarificationDialog from "../components/common/ClarificationDialog.vue";
import FlowDialog from "../components/common/FlowDialog.vue";
import RequestFlowDialog from "../components/common/RequestFlowDialog.vue";
import ApprovalDialog from "../components/common/ApprovalDialog.vue";
import TableExpenseDialog from "../components/common/TableExpenseDialog.vue";
import TableExpenseWithApprovalDialog from "../components/common/TableExpenseWithApprovalDialog.vue";
import PlanDialog from "../components/common/ApprovalDialogs/PlanDialog.vue";
import ActualDialog from "../components/common/ApprovalDialogs/ActualDialog.vue";
import VacationDialog from "../components/common/ApprovalDialogs/VacationDialog.vue";
import CommercialDialog from "../components/common/ApprovalDialogs/CommercialDialog.vue";
import ExpenseDialog from "../components/common/ApprovalDialogs/ExpenseDialog.vue";
import RamadanDialog from "../components/common/RamadanDialog.vue";
import ReplicateLine from "../components/common/ReplicateLine.vue";
import ReplicateMapping from "../components/common/ReplicateMapping.vue";
import { mapState, mapActions, mapMutations } from "vuex";
import CallDialog from "../components/group-video/CallDialog.vue";
import ProgressBar from "../components/common/ProgressBar.vue";

export default {
  name: "TheContainer",
  components: {
    TheSidebar: () => import("./TheSidebar"),
    TheHeader: () => import("./TheHeader"),
    TheFooter: () => import("./TheFooter"),
    // TheAside: () => import("./TheAside"),
    message: () => import("./../views/message"),
    SocketHandler: () => import("../components/group-video/SocketHandler.vue"),
    // MyDialog, //: () => import('../components/common/MyDialog.vue'),
    MapDialog, //: () => import('../components/common/MapDialog.vue'),
    DoctorDialog, //: () => import('../components/common/DoctorDialog.vue'),
    AccountDialog, //: () => import('../components/common/AccountDialog.vue'),
    TableDialog, //: () => import('../components/common/TableDialog.vue'),
    FlowDialog, //: () => import('../components/common/TableDialog.vue'),
    PlanDialog, //: () => import('../components/common/ApprovalDialogs/PlanDialog.vue'),
    ActualDialog, //: () => import('../components/common/ApprovalDialogs/ActualDialog.vue'),
    VacationDialog, //: () => import('../components/common/ApprovalDialogs/VacationDialog.vue'),
    CommercialDialog, //: () => import('../components/common/ApprovalDialogs/CommercialDialog.vue'),
    ExpenseDialog, //: () => import('../components/common/ApprovalDialogs/ExpenseDialog.vue'),
    // RamadanDialog,
    ClarificationDialog,
    RequestFlowDialog,
    ApprovalDialog,
    PrintDialog,
    ReplicateLine,
    ReplicateMapping,
    TableExpenseDialog,
    TableExpenseWithApprovalDialog,
    CallDialog, //: () => import('../components/group-video/CallDialog.vue'),
    ProgressBar, //: () => import('../components/common/ProgressBar.vue'),
    Samples, //: () => import('../components/common/ProgressBar.vue'),
  },
  data() {
    return {
      locale: "en",
      audio: null,
      renderComponent: true,
    };
  },
  methods: {
    ...mapActions("notifications", ["loadNotifications"]),
    ...mapActions("authentication", ["rollbackPersonate"]),
    ...mapMutations("notifications", ["setNotifications"]),
    changeLocale(value) {
      this.locale = value;
    },
    initialize() {
      this.loadNotifications();
      this.audio = document.createElement("audio");
      this.audio.src = "notification_message.wav";
    },
    rollback() {
      this.rollbackPersonate();
      this.$router.push({ name: "users" }).then(() => window.location.reload());
    },
  },
  computed: {
    ...mapState("app", ["vApp", "onlineChannel"]),
    ...mapState("authentication", ["imPersonated", "authUser"]),
    ...mapState("notifications", ["notifications"]),
  },
  mounted() {
    // this.$dialog.open = this.$refs.confirm.open;
    this.$root.$map = this.$refs.map.open;
    this.$root.$doctor = this.$refs.doctor.open;
    this.$root.$account = this.$refs.account.open;
    this.$root.$table = this.$refs.table.open;
    this.$root.$flow = this.$refs.flow.open;
    this.$root.$requestFlow = this.$refs.requestFlow.open;
    this.$root.$print = this.$refs.print.open;
    this.$root.$tableExpense = this.$refs.tableExpense.open;
    this.$root.$tableWithApprovalExpense =
      this.$refs.tableWithApprovalExpense.open;
    this.$root.$plan = this.$refs.plan.open;
    this.$root.$actual = this.$refs.actual.open;
    this.$root.$vacation = this.$refs.vacation.open;
    this.$root.$commercial = this.$refs.commercial.open;
    this.$root.$clarification = this.$refs.clarification.open;
    this.$root.$approval = this.$refs.approval.open;
    this.$root.$expense = this.$refs.expense.open;
    this.$root.$line = this.$refs.line.open;
    this.$root.$mapping = this.$refs.mapping.open;
    // this.$root.$ramadan = this.$refs.ramadan.open;
    this.$root.$call = this.$refs.call;
    this.$root.$progress = this.$refs.progress.open;
    this.$root.$samples = this.$refs.samples.open;
  },
  created() {
    this.initialize();
    this.onlineChannel.on("new notification", (notification) => {
      this.audio.play();
      this.setNotifications([...this.notifications, notification]);
    });
  },
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
